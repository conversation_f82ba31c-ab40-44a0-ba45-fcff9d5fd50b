<?php
namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\ClbType;
use App\Models\HeadDsgn;
use App\Models\Hossel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Auth;

class ModelsController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $models = HeadDsgn::with(['category'])
                ->latest();

            if ($request->has('status') && $request->status != '') {
                $models->where('status', $request->status == 'active' ? '1' : '0');
            }

            if ($request->has('parent') && $request->parent != '') {
                $models->whereHas('category', function ($query) use ($request) {
                    $query->where('ClbTypeDsc', 'like', "%" . $request->parent . "%");
                });
            }

            $models = $models->get();

            return DataTables::of($models)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox" value="' . $row->HeadDsgnId . '">';
                })
                ->addIndexColumn()
                ->addColumn('image', function ($row) {
                    $imageSrc = asset('website/' . $row->image());
                    $imageTooltip = htmlspecialchars('<img src="' . $imageSrc . '" class="img-fluid" />', ENT_QUOTES, 'UTF-8');
                    return '<img class="img-fluid preview-image" src="' . $imageSrc . '"
                            style="width: 45px;height: 45px;object-fit: cover;border-radius: 50%;"
                            data-bs-toggle="tooltip"
                            data-bs-placement="top"
                            data-bs-html="true"
                            title="' . $imageTooltip . '" />';
                })
                ->addColumn('code', function ($row) {
                    return $row->HeadDsgnCd ?? 'N/A';
                })
                ->addColumn('model', function ($row) {
                    return $row->HeadDsgnDsc ?? 'N/A';
                })
                ->addColumn('type', function ($row) {
                    return $row->type ?? 'N/A';
                })
                ->addColumn('side', function ($row) {
                    return $row->hnd_side ?? 'Both';
                })
                ->addColumn('category', function ($row) {
                    return $row->category->ClbTypeDsc ?? 'N/A';
                })
                ->addColumn('color', function ($row) {
                    $colors = $row->colors() ?? [];
                    $colorHtml = '';
                    foreach ($colors as $color) {
                        $colorHtml .= '<span class="color-box" style="background-color:' . $color . '; width: 20px; height: 20px;"></span>';
                    }
                    return $colorHtml ?: 'N/A';
                })
                ->addColumn('status', function ($row) {
                    return $row->statusHtml ?? 'N/A';
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li>
                                <a class="dropdown-item edit_model_button" href="javascript:void(0)"
                                   model_id="' . $row->HeadDsgnId . '"
                                   model_name="' . htmlspecialchars($row->HeadDsgnDsc, ENT_QUOTES, 'UTF-8') . '"
                                   model_image="' . htmlspecialchars(json_encode($row->images()), ENT_QUOTES, 'UTF-8') . '"
                                   model_colors="' . htmlspecialchars(json_encode($row->colors()), ENT_QUOTES, 'UTF-8') . '"
                                   model_club_numbers="' . htmlspecialchars(json_encode($row->club_numbers()), ENT_QUOTES, 'UTF-8') . '"
                                   model_has_loft="' . $row->has_loft . '"
                                   model_has_lie="' . $row->has_lie . '"
                                   model_has_faceangle="' . $row->has_faceangle . '"
                                   model_has_hossel="' . $row->has_hossel . '"
                                   model_category_id="' . $row->ClbTypeCd . '"
                                   model_side="' . $row->hnd_side . '"
                                   model_type="' . $row->type . '"
                                   model_status="' . $row->status . '"
                                   model_has_australia="' . $row->has_australia . '"
                                   model_has_united_states="' . $row->has_united_states . '"
                                   model_b2b_price_australia="' . $row->b2b_price_australia . '"
                                   model_b2b_price_us="' . $row->b2b_price_us . '"
                                   model_b2c_price_australia="' . $row->b2c_price_australia . '"
                                   model_b2c_price_us="' . $row->b2c_price_us . '"
                                   >Edit
                                </a>
                            </li>
                            <li>
                                <form action="' . route('models.destroy', $row->HeadDsgnId) . '" method="POST" class="delete-form">
                                    ' . csrf_field() . method_field('DELETE') . '
                                    <a class="dropdown-item" href="javascript:void(0)" onclick="showDeleteConfirmation(this)">Delete</a>
                                </form>
                            </li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['image', 'color', 'status', 'action', 'checkbox'])
                ->make(true);
        }

        $categories = ClbType::latest()->get();

        return view('dashboard.Cruds.Models.index', compact('categories'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'category_id' => 'required',
            'HeadDsgnDsc' => [
                'required',
                Rule::unique('headdsgn', 'HeadDsgnDsc')->where(function ($query) use ($request) {
                    return $query->where('ClbTypeCd', $request->category_id);
                }),
            ],
            'images' => 'nullable|array|min:1',
            'hnd_side' => 'required|in:Left,Right,Both',
            'type' => 'required|in:b2b,b2c,both',
            'has_australia' => [
                function ($attribute, $value, $fail) use ($request) {
                    if (!$request->has('has_australia') && !$request->has('has_united_states')) {
                        $fail('At least one manufacturing unit (Australia or United States) must be selected.');
                    }
                },
            ],
            'b2b_price_australia' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type, ['b2b', 'both']) && $request->has('has_australia');
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
            'b2b_price_us' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type, ['b2b', 'both']) && $request->has('has_united_states');
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
            'b2c_price_australia' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type, ['b2c', 'both']) && $request->has('has_australia');
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
            'b2c_price_us' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type, ['b2c', 'both']) && $request->has('has_united_states');
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
        ], [
            'category_id.required' => 'Please select a category.',
            'HeadDsgnDsc.required' => 'Model name is required.',
            'HeadDsgnDsc.unique' => 'Model name already taken for the selected parent category.',
            'hnd_side.required' => 'Please select a side.',
            'hnd_side.in' => 'The selected side is invalid.',
            'type.required' => 'Please select a type.',
            'type.in' => 'The selected type is invalid.',
            'has_australia' => 'At least one manufacturing unit (Australia or United States) must be selected.',
            'b2b_price_australia.required' => 'B2B Australia price is required.',
            'b2b_price_australia.numeric' => 'B2B Australia price must be a valid number.',
            'b2b_price_australia.max' => 'B2B Australia price cannot be more than 1,000,000.',
            'b2b_price_us.required' => 'B2B United States price is required.',
            'b2b_price_us.numeric' => 'B2B United States price must be a valid number.',
            'b2b_price_us.max' => 'B2B United States price cannot be more than 1,000,000.',
            'b2c_price_australia.required' => 'B2C Australia price is required.',
            'b2c_price_australia.numeric' => 'B2C Australia price must be a valid number.',
            'b2c_price_australia.max' => 'B2C Australia price cannot be more than 1,000,000.',
            'b2c_price_us.required' => 'B2C United States price is required.',
            'b2c_price_us.numeric' => 'B2C United States price must be a valid number.',
            'b2c_price_us.max' => 'B2C United States price cannot be more than 1,000,000.',
        ]);

        try {
            DB::beginTransaction();
            $model = HeadDsgn::create([
                'ClbTypeCd' => $request->category_id,
                'HeadDsgnCd' => strtoupper(implode('', array_map(function ($word) {
                    return strtoupper($word[0]);
                }, explode(' ', $request->HeadDsgnDsc ?? '')))),
                'HeadDsgnDsc' => $request->HeadDsgnDsc ?? null,
                'images' => json_encode($request->images ?? ['models/default.png']),
                'colors' => json_encode($request->colors ?? null),
                'has_loft' => $request->has_loft ?? 0,
                'has_lie' => $request->has_lie ?? 0,
                'has_faceangle' => $request->has_faceangle ?? 0,
                'has_hossel' => $request->has_hossel ?? 0,
                'club_numbers' => json_encode($request->club_numbers ?? null),
                'hnd_side' => $request->hnd_side ?? null,
                'type' => $request->type ?? null,
                'has_australia' => $request->has_australia ?? 0,
                'has_united_states' => $request->has_united_states ?? 0,
                'b2b_price_australia' => $request->b2b_price_australia ?? null,
                'b2b_price_us' => $request->b2b_price_us ?? null,
                'b2c_price_australia' => $request->b2c_price_australia ?? null,
                'b2c_price_us' => $request->b2c_price_us ?? null,
            ]);
            DB::commit();
            return redirect()->route('models.index')->with(['title' => 'Done', 'message' => 'Model created successfully.', 'type' => 'success']);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('models.index')->with(['title' => 'Fail', 'message' => 'Unable to create model: ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    public function update(Request $request, $id)
    {
        $model = HeadDsgn::where('HeadDsgnId', $request->model_id_update)->firstOrFail();

        $request->validate([
            'category_id_update' => 'required',
            'HeadDsgnDsc_update' => [
                'required',
                Rule::unique('headdsgn', 'HeadDsgnDsc')->where(function ($query) use ($request, $model) {
                    return $query->where('ClbTypeCd', $request->category_id_update)->where('HeadDsgnId', '!=', $model->HeadDsgnId);
                }),
            ],
            'images_update' => 'nullable|array|min:1',
            'update_hnd_side' => 'required|in:Left,Right,Both',
            'status' => 'required|in:0,1',
            'type_update' => 'required|in:b2b,b2c,both',
            'has_australia' => [
                function ($attribute, $value, $fail) use ($request) {
                    if (!$request->has('has_australia') && !$request->has('has_united_states')) {
                        $fail('At least one manufacturing unit (Australia or United States) must be selected.');
                    }
                },
            ],
            'b2b_price_australia_update' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type_update, ['b2b', 'both']) && $request->has('has_australia');
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
            'b2b_price_us_update' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type_update, ['b2b', 'both']) && $request->has('has_united_states');
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
            'b2c_price_australia_update' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type_update, ['b2c', 'both']) && $request->has('has_australia');
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
            'b2c_price_us_update' => [
                Rule::requiredIf(function () use ($request) {
                    return in_array($request->type_update, ['b2c', 'both']) && $request->has('has_united_states');
                }),
                'nullable',
                'numeric',
                'max:1000000',
            ],
        ], [
            'category_id_update.required' => 'Please select a category.',
            'HeadDsgnDsc_update.required' => 'Model name is required.',
            'HeadDsgnDsc_update.unique' => 'Model name already exists under the selected category.',
            'update_hnd_side.required' => 'Please select a side.',
            'update_hnd_side.in' => 'The selected side is invalid.',
            'status.required' => 'Please select a status.',
            'status.in' => 'The selected status is invalid.',
            'type_update.required' => 'Please select a type.',
            'type_update.in' => 'The selected type is invalid.',
            'has_australia' => 'At least one manufacturing unit (Australia or United States) must be selected.',
            'b2b_price_australia_update.required' => 'B2B Australia price is required.',
            'b2b_price_australia_update.numeric' => 'B2B Australia price must be a valid number.',
            'b2b_price_australia_update.max' => 'B2B Australia price cannot be more than 1,000,000.',
            'b2b_price_us_update.required' => 'B2B United States price is required.',
            'b2b_price_us_update.numeric' => 'B2B United States price must be a valid number.',
            'b2b_price_us_update.max' => 'B2B United States price cannot be more than 1,000,000.',
            'b2c_price_australia_update.required' => 'B2C Australia price is required.',
            'b2c_price_australia_update.numeric' => 'B2C Australia price must be a valid number.',
            'b2c_price_australia_update.max' => 'B2C Australia price cannot be more than 1,000,000.',
            'b2c_price_us_update.required' => 'B2C United States price is required.',
            'b2c_price_us_update.numeric' => 'B2C United States price must be a valid number.',
            'b2c_price_us_update.max' => 'B2C United States price cannot be more than 1,000,000.',
            'images_update.array' => 'Please upload at least one valid image.',
        ]);

        try {
            DB::beginTransaction();

            if ($request->has('removed_images') && count($request->removed_images) > 0) {
                foreach ($request->removed_images as $removedImg) {
                    $this->deleteImage($removedImg);
                }
            }

            $model->update([
                'ClbTypeCd' => $request->category_id_update,
                'HeadDsgnCd' => strtoupper(implode('', array_map(function ($word) {
                    return strtoupper($word[0]);
                }, explode(' ', $request->HeadDsgnDsc_update ?? '')))),
                'HeadDsgnDsc' => $request->HeadDsgnDsc_update ?? null,
                'images' => json_encode($request->images_update ?? ($model->images ?? ['models/default.png'])),
                'colors' => json_encode($request->colors ?? ($model->colors ?? null)),
                'status' => $request->status ?? null,
                'has_loft' => $request->has_loft ?? 0,
                'has_lie' => $request->has_lie ?? 0,
                'has_faceangle' => $request->has_faceangle ?? 0,
                'has_hossel' => $request->has_hossel ?? 0,
                'club_numbers' => isset($request->club_no) && $request->club_no == 1
                    ? (isset($request->club_numbers) ? json_encode($request->club_numbers) : ($model->club_numbers ?? null))
                    : null,
                'hnd_side' => $request->update_hnd_side ?? null,
                'type' => $request->type_update ?? null,
                'has_australia' => $request->has_australia ?? null,
                'has_united_states' => $request->has_united_states ?? null,
                'b2b_price_australia' => $request->b2b_price_australia_update ?? null,
                'b2b_price_us' => $request->b2b_price_us_update ?? null,
                'b2c_price_australia' => $request->b2c_price_australia_update ?? null,
                'b2c_price_us' => $request->b2c_price_us_update ?? null,
            ]);

            DB::commit();

            return redirect()->route('models.index')->with([
                'title' => 'Done',
                'message' => 'Model updated successfully.',
                'type' => 'success',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('models.index')->with([
                'title' => 'Fail',
                'message' => 'Update failed: ' . $e->getMessage(),
                'type' => 'error',
            ]);
        }
    }

    public function destroy($id)
    {
        try {
            HeadDsgn::where('HeadDsgnId', $id)->update(['deleted_at' => now()]);
            return redirect()->route('models.index')->with(['title' => 'Done', 'message' => 'Model deleted successfully.', 'type' => 'success']);
        } catch (\Exception $e) {
            return redirect()->route('models.index')->with(['title' => 'Fail', 'message' => 'Unable to delete model: ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $model = HeadDsgn::where('HeadDsgnId', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();
            $model->status = $request->status;
            $model->save();
            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Model status updated successfully.',
                'status' => $model->status,
                'resultHtml' => ($model->status == 1) ? '<span style="cursor:pointer;" class="success change-model-status" model_id="' . $model->HeadDsgnId . '" model_status="0">Active</span>' : '<span style="cursor:pointer;" class="danger change-model-status" model_id="' . $model->HeadDsgnId . '" model_status="1">Inactive</span>'
            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update model status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function uploadModelImages(Request $request)
    {
        try {
            $request->validate([
                'image' => 'required|min:1',
                'image.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            ], [
                'image.required' => 'Please upload at least one image.',
                'image.*.image' => 'Each file must be an image.',
                'image.*.mimes' => 'Only PNG, JPEG, JPG, and GIF images are allowed.',
                'image.*.max' => 'The image size must be less than 2MB.',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Image uploaded successfully.',
                'image' => $this->storeImage('models', $request->file('image')),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to upload image, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function deleteModelImage(Request $request)
    {
        try {
            $request->validate([
                'filename' => 'required|min:1',
            ], [
                'filename.required' => 'Please upload at least one image.',
            ]);

            $this->deleteImage($request->filename);

            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully.',
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to delete image, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getModels(Request $request, $category_id = null)
    {
        try {
            $side = $request->query('side');
            $manufLocId = optional(optional(optional(Auth::user())->fitter)->account)->ManufLocID;
            $query = HeadDsgn::where('ClbTypeCd', $category_id)
                ->where('status', '1')
                ->whereIn('type', ['b2b', 'both']);
            if ($manufLocId == "3") {
                $query->where('has_australia','3');
            } elseif ($manufLocId == "5") {
                $query->where('has_united_states','5');
            }
            if (!is_null($side)) {
                $query->where(function ($q) use ($side) {
                    $q->where('hnd_side', $side)
                        ->orWhere('hnd_side', 'Both');
                });
            }
            $models = $query->get(['HeadDsgnId', 'HeadDsgnDsc']);
            return response()->json($models);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch models, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getColors($model_id = null)
    {
        try {
            $model = HeadDsgn::where('HeadDsgnId', $model_id)->with('hossels')->firstOrFail();
            $colors = json_decode($model->colors, true) ?? [];
            $colorNames = config('color.names', []);
            $colorOptions = array_map(function ($color) use ($colorNames) {
                return [
                    'id' => $color,
                    'name' => $colorNames[$color] ?? $color
                ];
            }, $colors);
            return response()->json([
                'color_options' => $colorOptions,
                'model' => $model
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch colors, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
