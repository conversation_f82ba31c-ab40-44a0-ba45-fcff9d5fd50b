<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\ClubPricingService;
use App\Models\HeadDsgn;
use App\Models\User;
use App\Models\Ftr;
use App\Models\Acct;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;

class ManufacturingLocationPricingTest extends TestCase
{
    use RefreshDatabase;

    protected $pricingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->pricingService = new ClubPricingService();
    }

    /** @test */
    public function it_returns_australia_b2b_price_for_australia_location()
    {
        // Create test model with location-specific pricing
        $model = HeadDsgn::create([
            'ClbTypeCd' => 'TEST',
            'HeadDsgnCd' => 'TEST',
            'HeadDsgnDsc' => 'Test Model',
            'type' => 'b2b',
            'has_australia' => 3,
            'has_united_states' => 0,
            'b2b_price_australia' => 150.00,
            'b2b_price_us' => 120.00,
            'status' => 1
        ]);

        // Create user with Australia manufacturing location
        $account = Acct::create(['ManufLocID' => '3']);
        $fitter = Ftr::create(['AcctId' => $account->AcctId]);
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
        $fitter->update(['UserId' => $user->id]);

        Auth::login($user);

        $clubData = [
            'category' => [
                'model' => [
                    'model_id' => $model->HeadDsgnId
                ]
            ]
        ];

        $breakdown = $this->pricingService->getPricingBreakdown($clubData);

        $this->assertEquals(150.00, $breakdown['model_price']);
        $this->assertEquals('AUD', $breakdown['currency']);
        $this->assertEquals('A$', $breakdown['currency_symbol']);
        $this->assertEquals('3', $breakdown['manufacturing_location']);
    }

    /** @test */
    public function it_returns_us_b2b_price_for_us_location()
    {
        // Create test model with location-specific pricing
        $model = HeadDsgn::create([
            'ClbTypeCd' => 'TEST',
            'HeadDsgnCd' => 'TEST',
            'HeadDsgnDsc' => 'Test Model',
            'type' => 'b2b',
            'has_australia' => 0,
            'has_united_states' => 5,
            'b2b_price_australia' => 150.00,
            'b2b_price_us' => 120.00,
            'status' => 1
        ]);

        // Create user with US manufacturing location
        $account = Acct::create(['ManufLocID' => '5']);
        $fitter = Ftr::create(['AcctId' => $account->AcctId]);
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
        $fitter->update(['UserId' => $user->id]);

        Auth::login($user);

        $clubData = [
            'category' => [
                'model' => [
                    'model_id' => $model->HeadDsgnId
                ]
            ]
        ];

        $breakdown = $this->pricingService->getPricingBreakdown($clubData);

        $this->assertEquals(120.00, $breakdown['model_price']);
        $this->assertEquals('USD', $breakdown['currency']);
        $this->assertEquals('$', $breakdown['currency_symbol']);
        $this->assertEquals('5', $breakdown['manufacturing_location']);
    }

    /** @test */
    public function it_handles_both_type_models_correctly()
    {
        // Create test model that supports both B2B and B2C
        $model = HeadDsgn::create([
            'ClbTypeCd' => 'TEST',
            'HeadDsgnCd' => 'TEST',
            'HeadDsgnDsc' => 'Test Model Both',
            'type' => 'both',
            'has_australia' => 3,
            'has_united_states' => 5,
            'b2b_price_australia' => 150.00,
            'b2b_price_us' => 120.00,
            'b2c_price_australia' => 200.00,
            'b2c_price_us' => 180.00,
            'status' => 1
        ]);

        // Test with Australia location
        $account = Acct::create(['ManufLocID' => '3']);
        $fitter = Ftr::create(['AcctId' => $account->AcctId]);
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
        $fitter->update(['UserId' => $user->id]);

        Auth::login($user);

        $clubData = [
            'category' => [
                'model' => [
                    'model_id' => $model->HeadDsgnId
                ]
            ]
        ];

        $breakdown = $this->pricingService->getPricingBreakdown($clubData);

        // Should return B2B price for Australia (assuming B2B request)
        $this->assertTrue(in_array($breakdown['model_price'], [150.00, 200.00]));
        $this->assertEquals('AUD', $breakdown['currency']);
        $this->assertEquals('A$', $breakdown['currency_symbol']);
    }
}
