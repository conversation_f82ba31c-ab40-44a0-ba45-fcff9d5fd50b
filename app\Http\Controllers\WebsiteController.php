<?php

namespace App\Http\Controllers;

use App\Models\ClbType;
use App\Models\Cntry;
use App\Models\Cust;
use App\Models\FaceAngle;
use App\Models\GripSz;
use App\Models\GripType;
use App\Models\Lie;
use App\Models\Loft;
use App\Models\Ord;
use App\Models\Profile;
use App\Models\ShfLen;
use App\Models\User;
use App\Services\CouponService;
use Illuminate\Http\Request;
use Auth;
use Illuminate\Support\Facades\Cache;
use Mail;

class WebsiteController extends Controller
{
    public function index()
    {
        return view('website.index');
    }

    public function aboutUs()
    {
        return view('website.about-us');
    }

    public function products()
    {
        return view('website.products');
    }

    public function productDetail()
    {
        return view('website.product-detail');
    }

    public function hgProcess()
    {
        return view('website.hg-process');
    }

    public function contactUs()
    {
        return view('website.contact-us');
    }

    public function fitter()
    {
        return view('website.fitter');
    }

    public function locateFitter()
    {
        return view('website.locate-fitter');
    }

    public function fitterDetail()
    {
        return view('website.fitter-detail');
    }

    public function cart()
    {
        return view('website.cart');
    }

    public function checkout()
    {
        $data = session('order_data', [
            'clubs' => [],
            'order' => null,
        ]);
        $clubs = collect($data['clubs'])->map(function ($club) {
            return (object) $club;
        });
        $order = (object) ($data['order'] ?? []);
        $customer = (object) ($data['customer_data']['customer_data'] ?? []);
        $coupon = $data['coupon'] ?? null;
        $countries = Cntry::get();

        // Get currency information based on manufacturing location
        $manufLocId = optional(optional(optional(\Auth::user())->fitter)->account)->ManufLocID;
        $currency = $this->getCurrencyByLocation($manufLocId);
        $currencySymbol = $this->getCurrencySymbol($manufLocId);

        return view('website.checkout', [
            'clubs' => $clubs,
            'order' => $order,
            'coupon' => $coupon,
            'customer' => $customer,
            'countries' => $countries,
            'currency' => $currency,
            'currencySymbol' => $currencySymbol,
            'manufLocId' => $manufLocId,
        ]);
    }

    public function orderClub(Request $request)
    {
        $countries = Cntry::get();
        $categories = ClbType::where('order_club_status', 'true')->get();
        $lieAngles = Lie::where('status', '1')->get();
        $faceAngles = FaceAngle::where('status', '1')->get();
        $lofts = Loft::where('status', '1')->get();
        $gripTypes = GripType::where('status', '1')->get();
        $gripSize = GripSz::where('status', '1')->get();
        $shfLens = ShfLen::where('status','1')->get();
        $customers = Cust::where('FtrId', Auth::user()->fitter->FtrId)->where('status', '1')->get();
//        $formData = Cache::get('order_form_draft_' . auth()->user()->id);
        $formData = null;
        if (!empty($formData)) {
            $clubs = isset($formData['clubs']) ? $formData['clubs'] : [];
        } else {
            $clubs = [];
        }
        session()->forget('order_data');
        return view('website.fitter-pages.order-club', compact('lofts' ,'countries', 'categories', 'gripTypes', 'gripSize', 'customers', 'formData', 'clubs','lieAngles','faceAngles','shfLens'));
    }

    public function sweetSpot()
    {
        return view('website.fitter-pages.sweetSpot');
    }

    public function shoppingCart()
    {
        $data = session('order_data', [
            'clubs' => [],
            'order' => null,
        ]);
        $clubs = collect($data['clubs'])->map(function ($club) {
            return (object) $club;
        });
        $order = (object) ($data['order'] ?? []);
        $coupon = $data['coupon'] ?? null;

        // Get currency information based on manufacturing location
        $manufLocId = optional(optional(optional(\Auth::user())->fitter)->account)->ManufLocID;
        $currency = $this->getCurrencyByLocation($manufLocId);
        $currencySymbol = $this->getCurrencySymbol($manufLocId);

        return view('website.fitter-pages.shopping-cart', [
            'clubs' => $clubs,
            'order' => $order,
            'coupon' => $coupon,
            'currency' => $currency,
            'currencySymbol' => $currencySymbol,
            'manufLocId' => $manufLocId,
        ]);
    }

    public function fitterCheckout()
    {
        return view('website.fitter-pages.fitter-checkout');
    }

    public function testing()
    {
        return 'testing';
        // Step 1: Get customers with NULL, empty, or invalid email
        $customers = Cust::where(function ($query) {
            $query->whereNull('EmailAddr')
                ->orWhere('EmailAddr', '')
                ->orWhereRaw("EmailAddr NOT REGEXP '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'")
                ->orWhereRaw("EmailAddr REGEXP '^[0-9]+$'");
        })
            ->get()
            // Step 2: Filter by unique first name + last name (case-insensitive)
            ->unique(function ($customer) {
                return $customer->DfltNameOnTag;
            });
        // Step 3: Create users
        foreach ($customers as $customer) {
            $firstName = strtolower(preg_replace('/\s+/', '', $customer->FirstNm));
            $lastName = strtolower(preg_replace('/\s+/', '', $customer->LastNm));
            $generatedEmail = $firstName . '.' . $lastName . '@yopmail.com';
            $email = $generatedEmail;

            $user = User::updateOrCreate(
                ['email' => $email],
                [
                    'name' => $customer->FirstNm . ' ' . $customer->LastNm,
                    'password' => bcrypt(env('DEFAULT_PASSWORD')),
                ]
            );

            Profile::updateOrCreate(
                ['user_id' => $user->id],
                [
                    'pic' => 'fitters/default.png',
                    'address' => $customer->Addr,
                    'phone_number' => $customer->PhnNum,
                ]
            );

            if (!$user->hasRole('customer')) {
                $user->assignRole('customer');
            }
        }
    }
    public function repairClub(){
        return view('website.repair-club');
    }

    /**
     * Apply coupon using CouponService
     */
    public function applyCoupon(Request $request)
    {
        try {
            $couponService = new CouponService();
            $result = $couponService->applyCoupon($request->input('coupon_code'));

            if ($result['success']) {
                return response()->json($result);
            } else {
                return response()->json($result, 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while applying the coupon',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove coupon using CouponService
     */
    public function removeCoupon(Request $request)
    {
        try {
            $couponService = new CouponService();
            $result = $couponService->removeCoupon();

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while removing the coupon'
            ], 500);
        }
    }

    /**
     * Get currency code based on manufacturing location
     *
     * @param string|null $manufLocId
     * @return string
     */
    private function getCurrencyByLocation($manufLocId): string
    {
        if ($manufLocId == "3") { // Australia
            return 'AUD';
        } elseif ($manufLocId == "5") { // United States
            return 'USD';
        }

        // Default to USD if location not specified
        return 'USD';
    }

    /**
     * Get currency symbol based on manufacturing location
     *
     * @param string|null $manufLocId
     * @return string
     */
    private function getCurrencySymbol($manufLocId): string
    {
        if ($manufLocId == "3") { // Australia
            return 'A$';
        } elseif ($manufLocId == "5") { // United States
            return '$';
        }

        // Default to USD symbol if location not specified
        return '$';
    }

}

