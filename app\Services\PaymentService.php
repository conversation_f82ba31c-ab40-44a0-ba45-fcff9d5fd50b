<?php

namespace App\Services;

use Exception;

class PaymentService
{
    private $staxApiUrl;
    private $staxApiKey;

    public function __construct()
    {
        $this->staxApiUrl = config('services.stax.api_url', 'https://apiprod.fattlabs.com/charge');
        $this->staxApiKey = config('services.stax.api_key', env('STAX_API_KEY'));
    }

    /**
     * Process payment based on manufacturing location
     * ManufLocID = "3" (Australia) -> Stripe with AUD
     * ManufLocID = "5" (United States) -> Stax with USD
     */
    public function processPayment($paymentMethodId, $orderData, $clubs, $coupon = null)
    {
        // Get manufacturing location from authenticated user
        $manufLocId = optional(optional(optional(\Auth::user())->fitter)->account)->ManufLocID;

        \Log::info('Processing payment based on manufacturing location', [
            'manufacturing_location' => $manufLocId,
            'payment_method_id' => $paymentMethodId
        ]);

        if ($manufLocId == "3") {
            // Australia - Use Stripe with AUD
            return $this->processStripePayment($paymentMethodId, $orderData, $clubs, $coupon);
        } elseif ($manufLocId == "5") {
            // United States - Use Stax with USD
            return $this->processStaxPayment($paymentMethodId, $orderData, $clubs, $coupon);
        } else {
            // Default to Stax if location not specified
            \Log::warning('Manufacturing location not specified, defaulting to Stax', [
                'manufacturing_location' => $manufLocId
            ]);
            return $this->processStaxPayment($paymentMethodId, $orderData, $clubs, $coupon);
        }
    }

    /**
     * Process payment with Stax API
     */
    public function processStaxPayment($paymentMethodId, $orderData, $clubs, $coupon = null)
    {
        try {
            // Validate payment method ID
            if (empty($paymentMethodId)) {
                return [
                    'success' => false,
                    'message' => 'Payment method is required'
                ];
            }

            // Validate API key
            if (empty($this->staxApiKey)) {
                return [
                    'success' => false,
                    'message' => 'Payment processor configuration error: API key not set'
                ];
            }

            \Log::info('Starting Stax Payment Process', [
                'payment_method_id' => $paymentMethodId,
                'api_url' => $this->staxApiUrl,
                'has_api_key' => !empty($this->staxApiKey)
            ]);

            $paymentData = $this->preparePaymentData($paymentMethodId, $orderData, $clubs, $coupon);
            $response = $this->makeStaxApiCall($paymentData);

            return $this->handleStaxResponse($response);

        } catch (Exception $e) {
            \Log::error('Stax Payment Exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Payment processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process payment with Stripe API (for Australia - AUD)
     */
    public function processStripePayment($paymentMethodId, $orderData, $clubs, $coupon = null)
    {
        try {
            // Validate payment method ID
            if (empty($paymentMethodId)) {
                return [
                    'success' => false,
                    'message' => 'Payment method is required'
                ];
            }

            \Log::info('Starting Stripe Payment Process', [
                'payment_method_id' => $paymentMethodId,
                'currency' => 'AUD'
            ]);

            $paymentData = $this->prepareStripePaymentData($paymentMethodId, $orderData, $clubs, $coupon);

            // For now, return success with payment data
            // TODO: Implement actual Stripe API integration
            \Log::info('Stripe Payment Data Prepared', $paymentData);

            return [
                'success' => true,
                'message' => 'Stripe payment processed successfully (AUD)',
                'payment_data' => $paymentData,
                'gateway' => 'Stripe',
                'currency' => 'AUD'
            ];

        } catch (Exception $e) {
            \Log::error('Stripe Payment Error', [
                'error' => $e->getMessage(),
                'payment_method_id' => $paymentMethodId
            ]);

            return [
                'success' => false,
                'message' => 'Stripe payment processing failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Prepare payment data for Stax API
     */
    private function preparePaymentData($paymentMethodId, $orderData, $clubs, $coupon)
    {
        $order = (object) $orderData;

        // Get manufacturing location for currency determination
        $manufLocId = optional(optional(optional(\Auth::user())->fitter)->account)->ManufLocID;
        $currency = $this->getCurrencyByLocation($manufLocId);
        $currencySymbol = $this->getCurrencySymbol($manufLocId);

        // Calculate subtotal from clubs data (before any discounts)
        $subtotal = 0;
        if (is_array($clubs) && !empty($clubs)) {
            $subtotal = $this->calculateSubtotalFromClubs($clubs);
        }

        // If no clubs or calculation failed, get original total before discount
        if ($subtotal <= 0) {
            // If coupon is applied, get original total before discount
            if ($coupon && isset($coupon['original_total'])) {
                $subtotal = $coupon['original_total'];
            } else {
                $subtotal = $order->TtlAmt ?? 0;
            }
        }

        // Calculate discount from original subtotal
        $discountAmount = $this->calculateDiscount($subtotal, $coupon);
        $tax = $this->calculateTax($orderData);
        $shippingAmount = $this->calculateShipping($orderData);

        // Calculate final total
        $total = $subtotal - $discountAmount + $tax + $shippingAmount;

        // Ensure minimum total amount (Stax might have minimum requirements)
        if ($total < 1.00) {
            $total = 1.00; // Set minimum $1.00 for testing
        }

        // Prepare line items
        $lineItems = $this->prepareLineItems($clubs);

        // Ensure we have at least one line item
        if (empty($lineItems)) {
            $lineItems = [
                [
                    'id' => 'default-item',
                    'item' => 'Golf Club Order',
                    'details' => 'Custom Golf Club Order',
                    'quantity' => 1,
                    'price' => $total
                ]
            ];
        }

        // Note: Stax API accepts dollars, not cents (it converts internally)

        $paymentData = [
            'payment_method_id' => $paymentMethodId,
            'meta' => [
                'tax' => (float) $tax,
                'poNumber' => $order->OrdNo ?? 'ORD-' . time(),
                'shippingAmount' => (float) $shippingAmount,
                'payment_note' => $this->buildPaymentNote($order, $clubs),
                'subtotal' => (float) $subtotal,
                'lineItems' => $lineItems,
                'currency' => $currency,
                'currency_symbol' => $currencySymbol,
                'manufacturing_location' => $manufLocId
            ],
            'total' => (float) $total,
            'pre_auth' => 0
        ];

        // Calculate line items total and ensure it matches
        $lineItemsTotal = array_sum(array_map(function($item) {
            return $item['price'] * $item['quantity'];
        }, $lineItems));

        // If line items total doesn't match subtotal, use line items total as subtotal
        if (abs($lineItemsTotal - $subtotal) > 0.01) {
            \Log::warning('Line items total mismatch - adjusting subtotal', [
                'original_subtotal' => $subtotal,
                'line_items_total' => $lineItemsTotal,
                'difference' => abs($lineItemsTotal - $subtotal)
            ]);

            // Use line items total as the correct subtotal
            $subtotal = $lineItemsTotal;

            // Recalculate total with correct subtotal
            $total = $subtotal - $discountAmount + $tax + $shippingAmount;

            // Update payment data with corrected values
            $paymentData['meta']['subtotal'] = (float) $subtotal;
            $paymentData['total'] = (float) $total;
        }

        // Log payment data for debugging
        \Log::info('Prepared Payment Data', [
            'payment_method_id' => $paymentMethodId,
            'order_number' => $order->OrdNo ?? 'N/A',
            'final_total' => $total,
            'calculated_subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shippingAmount,
            'discount_amount' => $discountAmount,
            'line_items_count' => count($lineItems),
            'club_count' => is_array($clubs) ? count($clubs) : 0,
            'original_order_total' => $order->TtlAmt ?? 'N/A',
            'coupon_original_total' => $coupon['original_total'] ?? 'N/A',
            'coupon_discount_percentage' => $coupon['discount_percentage'] ?? 'N/A',
            'line_items_total' => array_sum(array_map(function($item) {
                return $item['price'] * $item['quantity'];
            }, $lineItems)),
            'calculation_breakdown' => [
                'subtotal' => $subtotal,
                'minus_discount' => $discountAmount,
                'plus_tax' => $tax,
                'plus_shipping' => $shippingAmount,
                'equals_total' => $total
            ]
        ]);

        return $paymentData;
    }

    /**
     * Prepare payment data for Stripe API (Australia - AUD)
     */
    private function prepareStripePaymentData($paymentMethodId, $orderData, $clubs, $coupon)
    {
        $order = (object) $orderData;

        // Get manufacturing location for currency determination
        $manufLocId = optional(optional(optional(\Auth::user())->fitter)->account)->ManufLocID;
        $currency = $this->getCurrencyByLocation($manufLocId);
        $currencySymbol = $this->getCurrencySymbol($manufLocId);

        // Calculate subtotal from clubs data (before any discounts)
        $subtotal = 0;
        if (is_array($clubs) && !empty($clubs)) {
            $subtotal = $this->calculateSubtotalFromClubs($clubs);
        }

        // If no clubs or calculation failed, get original total before discount
        if ($subtotal <= 0) {
            // If coupon is applied, get original total before discount
            if ($coupon && isset($coupon['original_total'])) {
                $subtotal = $coupon['original_total'];
            } else {
                $subtotal = $order->TtlAmt ?? 0;
            }
        }

        // Calculate discount from original subtotal
        $discountAmount = $this->calculateDiscount($subtotal, $coupon);
        $tax = $this->calculateTax($orderData);
        $shippingAmount = $this->calculateShipping($orderData);

        // Calculate final total
        $total = $subtotal - $discountAmount + $tax + $shippingAmount;

        // Ensure minimum total amount (Stripe might have minimum requirements)
        if ($total < 1.00) {
            $total = 1.00; // Set minimum $1.00 for testing
        }

        // Prepare line items
        $lineItems = $this->prepareLineItems($clubs);

        $paymentData = [
            'payment_method_id' => $paymentMethodId,
            'amount' => (int) ($total * 100), // Stripe expects amount in cents
            'currency' => strtolower($currency), // Stripe expects lowercase currency
            'metadata' => [
                'tax' => (float) $tax,
                'order_number' => $order->OrdNo ?? 'ORD-' . time(),
                'shipping_amount' => (float) $shippingAmount,
                'payment_note' => $this->buildPaymentNote($order, $clubs),
                'subtotal' => (float) $subtotal,
                'line_items_count' => count($lineItems),
                'currency_symbol' => $currencySymbol,
                'manufacturing_location' => $manufLocId,
                'discount_amount' => $discountAmount
            ],
            'confirm' => true,
            'return_url' => url('/payment/success') // Add your success URL
        ];

        // Log payment data for debugging
        \Log::info('Prepared Stripe Payment Data', [
            'payment_method_id' => $paymentMethodId,
            'order_number' => $order->OrdNo ?? 'N/A',
            'final_total' => $total,
            'amount_in_cents' => (int) ($total * 100),
            'currency' => $currency,
            'calculated_subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shippingAmount,
            'discount_amount' => $discountAmount,
            'line_items_count' => count($lineItems),
            'club_count' => is_array($clubs) ? count($clubs) : 0,
            'original_order_total' => $order->TtlAmt ?? 'N/A',
            'calculation_breakdown' => [
                'subtotal' => $subtotal,
                'minus_discount' => $discountAmount,
                'plus_tax' => $tax,
                'plus_shipping' => $shippingAmount,
                'equals_total' => $total
            ]
        ]);

        return $paymentData;
    }

    /**
     * Calculate discount amount
     */
    private function calculateDiscount($subtotal, $coupon)
    {
        if (!$coupon || !isset($coupon['discount_percentage'])) {
            return 0;
        }

        return ($subtotal * $coupon['discount_percentage']) / 100;
    }

    /**
     * Calculate tax from order data or default calculation
     */
    private function calculateTax($orderData)
    {
        $order = (object) $orderData;

        // Use tax from order data if available
        if (isset($order->TaxAmt) && $order->TaxAmt > 0) {
            return (float) $order->TaxAmt;
        }

        // Use tax rate if available
        if (isset($order->TaxRate) && isset($order->TtlAmt)) {
            return (float) ($order->TtlAmt * ($order->TaxRate / 100));
        }

        // Default: no tax
        return 0;
    }

    /**
     * Calculate shipping from order data or default calculation
     */
    private function calculateShipping($orderData)
    {
        $order = (object) $orderData;

        // Use shipping amount from order data if available
        if (isset($order->ShippingAmt) && $order->ShippingAmt > 0) {
            return (float) $order->ShippingAmt;
        }

        // Use shipping cost if available
        if (isset($order->ShippingCost)) {
            return (float) $order->ShippingCost;
        }

        // Default: no shipping
        return 0;
    }

    /**
     * Prepare line items for Stax API
     */
    private function prepareLineItems($clubs)
    {
        $lineItems = [];

        foreach ($clubs as $club) {
            $clubObj = (object) $club;

            // Build dynamic item name and details
            $itemName = $this->buildItemName($clubObj);
            $itemDetails = $this->buildItemDetails($clubObj);

            // Get correct price and quantity
            $quantity = (int) ($clubObj->Qty ?? 1);
            $pricePerItem = (float) ($clubObj->EachAmt ?? 0); // EachAmt is already per item price

            // For backward compatibility (server might have old code)
            $totalPrice = $pricePerItem * $quantity;

            // Log for debugging
            \Log::info('Club pricing debug', [
                'club_id' => $clubObj->ClbDtlId ?? 'N/A',
                'quantity' => $quantity,
                'each_amount' => $clubObj->EachAmt ?? 'N/A',
                'price_per_item' => $pricePerItem,
                'total_price' => $totalPrice
            ]);

            $lineItems[] = [
                'id' => $clubObj->ClbDtlId ?? uniqid(),
                'item' => $itemName,
                'details' => $itemDetails,
                'quantity' => $quantity,
                'price' => (float) $pricePerItem
            ];

            // Log individual club data for debugging
            \Log::info('Club Line Item', [
                'club_id' => $clubObj->ClbDtlId ?? 'N/A',
                'item_name' => $itemName,
                'quantity' => $quantity,
                'price_per_item' => $pricePerItem,
                'line_total' => $pricePerItem * $quantity,
                'raw_club_data' => (array) $clubObj
            ]);
        }

        return $lineItems;
    }

    /**
     * Build dynamic item name from club data
     */
    private function buildItemName($clubObj)
    {
        $parts = [];

        // Add model name if available
        if (!empty($clubObj->model->HeadDsgnCd)) {
            $parts[] = $clubObj->model->HeadDsgnCd .' - '. $clubObj->model->HeadDsgnDsc;
        }else {
            $parts[] = 'Golf Club';
        }

        // Add category if available
        if (!empty($clubObj->category)) {
            $parts[] = '(' . $clubObj->category->ClbTypeDsc . ')';
        }

        return implode(' ', $parts);
    }

    /**
     * Build dynamic item details from club data
     */
    private function buildItemDetails($clubObj)
    {
        $details = [];

        // Add shaft information
        if (!empty($clubObj->shaft->name)) {
            $details[] = 'Shaft: ' . $clubObj->shaft->name;
        }

        // Add specifications
        if (!empty($clubObj->Loft)) {
            $details[] = 'Loft: ' . $clubObj->Loft . '°';
        }
        if (!empty($clubObj->Lie)) {
            $details[] = 'Lie: ' . $clubObj->Lie . '°';
        }
        if (!empty($clubObj->FaceAngle)) {
            $details[] = 'Face: ' . $clubObj->FaceAngle . '°';
        }

        // Add side/hand
        if (!empty($clubObj->HndSide)) {
            $details[] = 'Hand: ' . $clubObj->HndSide;
        }

        return !empty($details) ? implode(', ', $details) : 'Custom Golf Club';
    }

    /**
     * Build dynamic payment note
     */
    private function buildPaymentNote($order, $clubs)
    {
        $clubCount = is_array($clubs) ? count($clubs) : 0;
        $orderNumber = $order->OrdNo ?? 'N/A';

        if ($clubCount > 0) {
            return "Henry Griffitts Golf Club Order #{$orderNumber} - {$clubCount} club(s)";
        }

        return "Henry Griffitts Golf Order #{$orderNumber}";
    }

    /**
     * Calculate subtotal from clubs if order total is missing
     */
    private function calculateSubtotalFromClubs($clubs)
    {
        $total = 0;

        if (is_array($clubs)) {
            foreach ($clubs as $club) {
                $clubObj = (object) $club;

                // EachAmt is per item price, multiply by quantity
                $quantity = (int) ($clubObj->Qty ?? 1);
                $pricePerItem = (float) ($clubObj->EachAmt ?? 0);
                $clubTotal = $pricePerItem * $quantity;

                $total += $clubTotal;

                \Log::info('Club subtotal calculation', [
                    'club_id' => $clubObj->ClbDtlId ?? 'N/A',
                    'quantity' => $quantity,
                    'price_per_item' => $pricePerItem,
                    'club_total' => $clubTotal,
                    'running_total' => $total
                ]);
            }
        }

        return $total;
    }

    /**
     * Make API call to Stax
     */
    private function makeStaxApiCall($paymentData)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://apiprod.fattlabs.com/charge");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($paymentData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Content-Type: application/json",
            "Authorization: Bearer " . $this->staxApiKey,
            "Accept: application/json"
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For testing only
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);  // For testing only

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Log the response for debugging
        \Log::info('Stax Payment Response', [
            'http_code' => $httpCode,
            'response' => $response,
            'curl_error' => $error
        ]);

        if ($error) {
            throw new Exception("cURL Error: " . $error);
        }

        return [
            'response' => $response,
            'http_code' => $httpCode
        ];
    }

    /**
     * Handle Stax API response
     */
    private function handleStaxResponse($apiResponse)
    {
        $responseData = json_decode($apiResponse['response'], true);

        // Log response data for debugging
        \Log::info('Stax Response Processing', [
            'http_code' => $apiResponse['http_code'],
            'response_data' => $responseData,
            'json_decode_error' => json_last_error_msg()
        ]);

        // Check for JSON decode errors
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'message' => 'Invalid response format from payment processor: ' . json_last_error_msg()
            ];
        }

        // Check for successful response (handle both 'success' and 'SUCCESS' status)
        $isSuccessful = $apiResponse['http_code'] === 200 &&
                       isset($responseData['success']) && $responseData['success'] === true;

        // Also check status field for SUCCESS (case insensitive)
        if (!$isSuccessful && isset($responseData['status'])) {
            $status = strtoupper($responseData['status']);
            $isSuccessful = $apiResponse['http_code'] === 200 && $status === 'SUCCESS';
        }

        if ($isSuccessful) {
            return [
                'success' => true,
                'transaction_id' => $responseData['id'] ?? null,
                'response' => $responseData
            ];
        }

        // Handle different error scenarios
        $errorMessage = 'Payment processing failed';

        if (isset($responseData['message'])) {
            $errorMessage = $responseData['message'];
        } elseif (isset($responseData['error_description'])) {
            $errorMessage = $responseData['error_description'];
        } elseif (isset($responseData['error'])) {
            $errorMessage = $responseData['error'];
        } elseif ($apiResponse['http_code'] === 401) {
            $errorMessage = 'Payment processor authentication failed';
        } elseif ($apiResponse['http_code'] === 400) {
            $errorMessage = 'Invalid payment data provided';
        } elseif ($apiResponse['http_code'] >= 500) {
            $errorMessage = 'Payment processor server error';
        }

        // Log the failure for debugging
        \Log::warning('Stax Payment Failed', [
            'http_code' => $apiResponse['http_code'],
            'error_message' => $errorMessage,
            'response_status' => $responseData['status'] ?? 'N/A',
            'response_success' => $responseData['success'] ?? 'N/A',
            'transaction_id' => $responseData['id'] ?? 'N/A'
        ]);

        return [
            'success' => false,
            'message' => $errorMessage,
            'http_code' => $apiResponse['http_code'],
            'response_data' => $responseData
        ];
    }

    /**
     * Get currency code based on manufacturing location
     *
     * @param string|null $manufLocId
     * @return string
     */
    private function getCurrencyByLocation($manufLocId): string
    {
        if ($manufLocId == "3") { // Australia
            return 'AUD';
        } elseif ($manufLocId == "5") { // United States
            return 'USD';
        }

        // Default to USD if location not specified
        return 'USD';
    }

    /**
     * Get currency symbol based on manufacturing location
     *
     * @param string|null $manufLocId
     * @return string
     */
    private function getCurrencySymbol($manufLocId): string
    {
        if ($manufLocId == "3") { // Australia
            return 'A$';
        } elseif ($manufLocId == "5") { // United States
            return '$';
        }

        // Default to USD symbol if location not specified
        return '$';
    }
}
