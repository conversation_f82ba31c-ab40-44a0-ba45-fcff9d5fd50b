<?php

namespace App\Http\Controllers;

use App\Models\BillCd;
use App\Models\ClbDtl;
use App\Models\ClbType;
use App\Models\Cust;
use App\Models\Ftr;
use App\Models\HeadDsgn;
use App\Models\NoChargeCode;
use App\Models\Ord;
use App\Models\Profile;
use App\Models\Shaft;
use App\Models\State;
use App\Models\User;
use App\Services\PaymentService;
use App\Services\OrderService;
use App\Traits\AddressHandler;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Yajra\DataTables\Facades\DataTables;
use Auth;
use App\Services\ClubPricingService;

class CustomerController extends Controller
{
    use AddressHandler;

    protected $paymentService;
    protected $orderService;

    public function __construct(PaymentService $paymentService, OrderService $orderService)
    {
        $this->paymentService = $paymentService;
        $this->orderService = $orderService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $customerQuery = Cust::orderByDesc('CustId');

            if ($request->has('search') && $request->search['value'] != '') {
                $searchValue = $request->search['value'];
                $customerQuery->where(function ($query) use ($searchValue) {
                    $query->where('FirstNm', 'like', "%$searchValue%")
                        ->orWhere('LastNm', 'like', "%$searchValue%")
                        ->orWhere('EMailAddr', 'like', "%$searchValue%")
                        ->orWhere('PhnNum', 'like', "%$searchValue%");
                });
            }

            if ($request->has('status') && $request->status != '') {
                $customerQuery->where('status', $request->status == 'active' ? '1' : '0');
            }

            return DataTables::of($customerQuery)
                ->addColumn('checkbox', function ($row) {
                    return '<input type="checkbox" class="category-checkbox">';
                })
                ->addColumn('name', function ($row) {
                    $imageSrc = asset('website/' . $row->image());
                    $imageTooltip = htmlspecialchars('<img src="' . $imageSrc . '" class="img-fluid" />', ENT_QUOTES, 'UTF-8');

                    return '<img class="img-fluid preview-image" src="' . $imageSrc . '"
                 style="width: 45px;height: 45px;object-fit: cover;border-radius: 50%;"
                data-bs-toggle="tooltip"
                data-bs-placement="top"
                data-bs-html="true"
                title="' . $imageTooltip . '" /> '
                        . $row->FirstNm . ' ' . $row->LastNm;
                })
                ->addColumn('email', function ($row) {
                    return $row->EMailAddr ?? 'N/A';
                })
                ->addColumn('phone', function ($row) {
                    return $row->PhnNum ?? 'N/A';
                })
                ->addColumn('total_orders', function ($row) {
                    return count($row->custOrders);
                })
                ->addColumn('status', function ($row) {
                    return $row->StatusHtml ?? 'N/A';
                })
                ->addColumn('action', function ($row) {
                    return '
                    <div class="dropdown">
                        <button class="btn dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                            <img class="ellipsis_img" src="' . asset('website') . '/assets/images/ellipsis.svg">
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                            <li><a class="dropdown-item" href="' . route('customers.show', $row->CustId) . '">View Profile</a></li>
                            <li><a class="dropdown-item" href="' . route('customer.view.orders', $row->CustId) . '">View Orders</a></li>
                        </ul>
                    </div>';
                })
                ->rawColumns(['status', 'action', 'name','checkbox'])
                ->make(true);
        }

        return view('dashboard.user-management.Customers.index');
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $customer = Cust::where('CustId', $id)->first();
        return view('dashboard.user-management.Customers.view', compact('customer'));
    }


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function orders(string $id)
    {
        return view('dashboard.user-management.Customers.order-view');

    }

    public function getRunningCustomerDetails($id){
        try {
            $detail = Cust::where('CustId', $id)->where('FtrId', Auth::user()->fitter->FtrId)->first();
            $orders = Ord::where('CustId',$detail->CustId)->get();
            return response()->json([
                'success' => true,
                'customer' => $detail,
                'orders' => $orders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch Customer details, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function clubOrderFormSubmit(Request $request)
    {
        $manufLocId = optional(optional(optional(Auth::user())->fitter)->account)->ManufLocID;
        // Calculate pricing but don't create order/clubs in database
        $cacheData = $request->all();
        $pricingService = new ClubPricingService();
        return $totalOrderAmount = $pricingService->calculateOrderTotal($cacheData['clubs']);


        $validationRules = [
            'customer_type' => 'required|in:new,running',
            'customer_gender' => 'required|in:male,female',
        ];
        if ($request->customer_type === 'new') {
            $validationRules = array_merge($validationRules, [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'phone_number' => 'required|string|max:15',
                'street_address' => 'required|string|max:255',
                'address_line_two' => 'nullable|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'zip_code' => 'required|string|max:10',
                'country_id' => 'required|string|max:3',
                'new_shipping_address' => 'required|string|max:500',
            ]);
        } elseif ($request->customer_type === 'running') {
            $validationRules = array_merge($validationRules, [
                'customer_id' => [
                    'required',
                    Rule::exists('cust', 'CustId')->where(function ($query) {
                        $query->where('FtrId', Auth::user()->fitter->FtrId);
                    }),
                ],
                'running_first_name' => 'required|string|max:255',
                'running_last_name' => 'required|string|max:255',
                'running_street_address' => 'required|string|max:255',
                'running_city' => 'required|string|max:255',
                'running_state' => 'required|string|max:255',
                'running_zip_code' => 'required|string|max:10',
                'running_email' => 'nullable|email',
                'running_phone_number' => 'nullable|string|max:15',
                'shipping_address' => 'required|string|max:500',
            ]);
        }
        $validationRules['clubs'] = 'required|array';
        $validationRules['clubs.*.category.model.quantity'] = 'required|integer|min:1';
        $request->validate($validationRules);
        try {
            $shippingAddress = null;
            $clubs = [];
            $cacheData = $request->all();

            // Prepare customer data for cache (don't create in database)
            $customerData = [];
            if ($request->customer_type === 'new') {
                $customerData = [
                    'type' => 'new',
                    'user_data' => [
                        'name' => $request->first_name . ' ' . $request->last_name,
                        'email' => $request->email,
                        'password' => bcrypt(env('DEFAULT_PASSWORD')),
                    ],
                    'profile_data' => [
                        'pic' => 'fitters/default.png',
                        'address' => $request->street_address,
                        'phone_number' => $request->phone_number,
                    ],
                    'customer_data' => [
                        'DfltNameOnTag' => $request->first_name . ' ' . $request->last_name,
                        'FtrId' => Auth::user()->fitter->FtrId,
                        'AcctId' => Auth::user()->fitter->AcctId,
                        'LastNm' => $request->last_name,
                        'FirstNm' => $request->first_name,
                        'CityNm' => $request->city,
                        'Addr' => $request->street_address,
                        'EMailAddr' => $request->email,
                        'PhnNum' => $request->phone_number,
                        'CntryCd' => $request->country_id,
                        'StateCd' => $request->state,
                        'PostalCd' => $request->zip_code,
                    ]
                ];
                $shippingAddress = $request->new_shipping_address;
            } else {
                // Get existing customer data for cache
                $customer = Cust::where('CustId', $request->customer_id)
                    ->where('FtrId', Auth::user()->fitter->FtrId)
                    ->firstOrFail();

                $customerData = [
                    'type' => 'running',
                    'customer_id' => $request->customer_id,
                    'existing_customer' => $customer, // Store existing customer object
                    'updated_data' => [
                        'AcctId' => Auth::user()->fitter->AcctId,
                        'LastNm' => $request->running_last_name,
                        'FirstNm' => $request->running_first_name,
                        'CityNm' => $request->running_city,
                        'Addr' => $request->running_street_address,
                        'EMailAddr' => $request->running_email,
                        'PhnNum' => $request->running_phone_number,
                        'DfltNameOnTag' => $request->running_first_name . ' ' . $request->running_last_name,
                        'StateCd' => $request->running_state,
                        'PostalCd' => $request->running_zip_code,
                    ]
                ];
                $shippingAddress = $request->shipping_address;
            }
            $manufLocId = optional(optional(optional(Auth::user())->fitter)->account)->ManufLocID;
            // Calculate pricing but don't create order/clubs in database
            $pricingService = new ClubPricingService();
            $totalOrderAmount = $pricingService->calculateOrderTotal($cacheData['clubs']);

            // Prepare order data for cache (don't create in database)
            $orderData = [
                'OrdTypeCd' => 'N',
                'AcctId' => Auth::user()->fitter->AcctId,
                'FtrId' => Auth::user()->fitter->FtrId,
                'OrdNo' => 'ORD-' . now()->format('YmdHis') . '-' . Str::random(4),
                'OrgDt' => now()->toDateString(),
                'ShipToAddr' => $shippingAddress,
                'ShipToCntryCd' => $request->country_id ?? $request->running_country_id ?? null,
                'ShipToCityNm' => $request->city ?? $request->running_city ?? null,
                'ShipToStateCd' => $request->state ?? $request->running_state ?? null,
                'ShipToPostalCd' => $request->zip_code ?? $request->running_zip_code ?? null,
                'CustAddr' => $request->street_address ?? $request->running_street_address ?? null,
                'CustCityNm' => $request->city ?? $request->running_city ?? null,
                'CustStateCd' => $request->state ?? $request->running_state ?? null,
                'CustPostalCd' => $request->zip_code ?? $request->running_zip_code ?? null,
                'BillAddr' => $shippingAddress,
                'BillCityNm' => $request->city ?? $request->running_city ?? null,
                'BillStateCd' => $request->state ?? $request->running_state ?? null,
                'BillPostalCd' => $request->zip_code ?? $request->running_zip_code ?? null,
                'OrdStatId' => 10,
                'BillToAcctID' => Auth::user()->fitter->AcctId,
                'TtlAmt' => $totalOrderAmount,
                'status' => 'pending',
            ];

            // Prepare club details for cache (don't create in database)
            foreach ($cacheData['clubs'] as $club) {
                $quantity = $club['category']['model']['quantity'] ?? 1;
                $clubPrice = $pricingService->calculateClubPrice($club);

                // Get category object from category_id
                $categoryId = $club['category']['category_id'] ?? null;
                $categoryObject = null;
                if ($categoryId) {
                    $categoryObject = ClbType::find($categoryId);
                }

                // Get model object from model_id
                $modelId = $club['category']['model']['model_id'] ?? null;
                $modelObject = null;
                if ($modelId) {
                    $modelObject = HeadDsgn::find($modelId);
                }

                $shaftId = $club['category']['shaft']['name'] ?? null;
                $shaftObject = null;
                if ($shaftId) {
                    $shaftObject = Shaft::find($shaftId);
                }

                for ($i = 0; $i < $quantity; $i++) {
                    $clubDetail = [
                        'HndSide' => $club['category']['side'] ?? null,
                        'ClbTypeCd' => $categoryId,
                        'category' => $categoryObject, // Category object
                        'HeadDsgnId' => $modelId,
                        'model' => $modelObject, // Model object
                        'Qty' => 1,
                        'EachAmt' => $clubPrice,
                        'Color' => $club['category']['model']['color'] ?? null,
                        'ClbNum' => $club['category']['model']['club_numbers'][$i] ?? null,
                        'LieId' => $club['category']['model']['lie_angle_id'] ?? null,
                        'FaceAngleId' => $club['category']['model']['faceangle_id'] ?? null,
                        'LoftId' => $club['category']['model']['loft_id'] ?? null,
                        'HslId' => $club['category']['model']['hossel_id'] ?? null,
                        // 'ShaftLenIncrement' => $club['category']['shaft_len_increment'] ?? null,
                        'SSTPure' => $club['category']['sst_pure'] ?? null,
                        'ShaftId' => $club['category']['shaft']['name'] ?? null,
                        'shaft' => $shaftObject, // Model object
                        'ShfTypeCd' => $club['category']['shaft']['material'] ?? null,
                        'ShfFlxCd' => $club['category']['shaft']['flex'] ?? null,
                        'StdShfLen' => $club['lengthData']['length'] ?? null,
                        'ShfLenId' => $club['lengthData']['range'] ?? null,
                        'GripTypeId' => $club['grip']['type'] ?? null,
                        'TopGripSzId' => $club['grip']['size'] ?? null,
                        'BotGripSzId' => $club['grip']['size'] ?? null,
                        'WrapSide' => $club['grip']['wrap-side'] ?? null,
                        'ExtraWrap' => $club['grip']['extra-wraps'] ?? null,
                        'ActvTypeId' => 1,

                        // Store original form data for reference
                        'original_club_data' => $club,
                    ];
                    $clubs[] = $clubDetail;
                }
            }
            Cache::forget('order_form_draft_' . Auth::id());
            session()->put('order_data', [
                'customer_data' => $customerData,
                'order' => $orderData,
                'clubs' => $clubs,
                'form_data' => $cacheData,
            ]);
            return redirect()->route('shopping-cart');
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'title' => 'Oops',
                'message' => 'Something went wrong! ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }

     public function orderFormSubmit(Request $request)
     {
         // Validate request using AddressHandler trait
         $validationRules = array_merge(
             ['payment_method' => 'required'],
             $this->validateAddressData($request)
         );
         $request->validate($validationRules);

         // Get session data
         $data = session('order_data', [
             'clubs' => [],
             'order' => null,
             'customer_data' => [],
             'form_data' => [],
             'coupon' => null,
         ]);

         try {
             // Process payment using PaymentService (automatically selects Stripe/Stax based on ManufLocID)
             $paymentResponse = $this->paymentService->processPayment(
                 $request->payment_method,
                 $data['order'] ?? [],
                 $data['clubs'] ?? [],
                 $data['coupon'] ?? null
             );

             if (!$paymentResponse['success']) {
                 return redirect()->back()->with([
                     'title' => 'Payment Failed',
                     'message' => $paymentResponse['message'],
                     'type' => 'error'
                 ]);
             }

             // Process address selection using AddressHandler trait
             $addressData = $this->processAddressSelection($request, $data);

             // Create order using OrderService
             $this->orderService->createOrder(
                 $data,
                 $data['clubs'] ?? [],
                 $data['customer_data']['customer_data'] ?? [],
                 $data['coupon'] ?? null,
                 $addressData,
                 $paymentResponse['transaction_id'] ?? null
             );

             // Clear session data
             Cache::forget('order_form_draft_' . Auth::id());
             session()->forget('order_data');

             return redirect()->route('order-club')->with([
                 'title' => 'Success',
                 'message' => 'Order has been placed successfully!',
                 'type' => 'success'
             ]);

         } catch (\Exception $e) {
             \Log::error('Order Form Submit Error', [
                 'error' => $e->getMessage(),
                 'trace' => $e->getTraceAsString(),
                 'request_data' => $request->all()
             ]);

             return redirect()->back()->with([
                 'title' => 'Oops',
                 'message' => 'Something went wrong! ' . $e->getMessage(),
                 'type' => 'error'
             ]);
         }
     }





    public function updateStatus(Request $request, $id)
    {
        if ($request->method() == 'GET') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request method.',
            ], 400);
        }

        $customer = Cust::where('CustId', $id)->firstOrFail();

        $request->validate([
            'status' => 'required|in:0,1',
        ], [
            'status.required' => 'Please select a status.',
            'status.in' => 'Status must be either Active (1) or Inactive (0).',
        ]);

        try {
            DB::beginTransaction();

            // Update status
            $customer->status = $request->status;
            $customer->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Customer status updated successfully.',
                'status' => $customer->status,
                'resultHtml' => ($customer->status==1)?'<span style="cursor:pointer;" class="success change-customer-status"  customer_id="'.$customer->CustId.'" customer_status="0" >Active</span>':'<span style="cursor:pointer;" class="danger change-customer-status"  customer_id="'.$customer->CustId.'" customer_status="1" >Inactive</span>'
            ], 200);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Unable to update fitter status, please try again.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    public function orderSaveDraft(Request $request)
    {
        $flatFormData = $request->all();
        $nestedFormData = [];
        foreach ($flatFormData as $key => $value) {
            Arr::set($nestedFormData, str_replace(['[', ']'], ['.', ''], $key), $value);
        }

        $existingCache = Cache::get('order_form_draft_' . auth()->user()->id, []);

        if (isset($nestedFormData['clubs']) && is_array($nestedFormData['clubs'])) {
            $existingClubs = isset($existingCache['clubs']) ? $existingCache['clubs'] : [];

            foreach ($nestedFormData['clubs'] as $newIndex => $newClub) {
                if (isset($existingClubs[$newIndex])) {
                    if ($existingClubs[$newIndex] != $newClub) {
                        $existingClubs[$newIndex] = $newClub;
                    }
                } else {
                    $existingClubs[$newIndex] = $newClub;
                }
            }

            $nestedFormData['clubs'] = $existingClubs;
        }

        Cache::put('order_form_draft_' . auth()->user()->id, $nestedFormData, now()->addWeek());
        return response()->json(['message' => 'Draft saved successfully!']);
    }

    public function getFtrCustomers($fitterId)
    {
        $customers = Ftr::find($fitterId)->ftrCustomers()->get();

        return response()->json([
            'customers' => $customers->map(function ($customer) {
                return [
                    'id' => $customer->CustId,
                    'text' => $customer->FirstNm . ' ' . $customer->LastNm ,
                ];
            }),
        ]);
    }



}
